{"version": 2, "uicontroller": "commitStatus.uicontroller.ts", "uimodel": {"nodes": {"pythonServerCommits": {"type": "list", "model": "urn:evst:everest:eve:model/node:Eve", "repoStatus": {"repo": "python-server", "imageName": "pythonServer"}, "fieldList": ["version"]}, "appserverCommits": {"type": "list", "model": "urn:evst:everest:eve:model/node:Eve", "repoStatus": {"repo": "appserver", "imageName": "appserver"}, "fieldList": ["version"]}, "aiServerCommits": {"type": "list", "model": "urn:evst:everest:eve:model/node:Eve", "repoStatus": {"repo": "ai-server", "imageName": "everestAiServer"}, "fieldList": ["version"]}, "uiCommits": {"type": "list", "model": "urn:evst:everest:eve:model/node:Eve", "uiStatus": {}, "fieldList": ["version"]}, "contentCommits": {"type": "list", "model": "urn:evst:everest:eve:model/node:Eve", "contentStatus": {}, "fieldList": ["version", "eveVersion", "baseAiVersion", "psaVersion"]}}}, "uiview": {"templateType": "details", "title": "Deployment Dashboard", "config": {"stretch": true, "allowRefreshData": true}, "header": {"content": {"title": "Deployment Dashboard"}}, "steps": {"variant": "default", "background": "grey", "content": [{"title": "Content", "components": [{"component": "Table", "customId": "contentCommitsTable", "section": {"editing": false, "grid": {"size": "12"}}, "props": {"data": "@binding:contentCommits", "variant": "light", "rowSelection": false, "onRowClicked": "@controller:redirectToMrPage", "columns": [{"field": "eveVersion", "headerName": "Eve Version"}, {"field": "baseAiVersion", "headerName": "Base.AI Version"}, {"field": "psaVersion", "headerName": "PSA Version"}, {"field": "message", "headerName": "Message"}, {"field": "author", "headerName": "Author"}, {"field": "date", "headerName": "Date"}, {"field": "deployedToAppdev", "headerName": "Appdev", "cellVariant": {"variant": "status", "matchers": "@controller:getAppdevStatusColor"}}, {"field": "deployedToEverestRuns", "headerName": "ERE", "cellVariant": {"variant": "status", "matchers": "@controller:getEREStatusColor"}}, {"field": "deployedToSummit", "headerName": "Summit", "cellVariant": {"variant": "status", "matchers": "@controller:getSummitStatusColor"}}]}}]}, {"title": "UI", "components": [{"component": "Table", "customId": "uiCommitsTable", "section": {"editing": false, "grid": {"size": "12"}}, "props": {"data": "@binding:uiCommits", "variant": "light", "rowSelection": false, "onRowClicked": "@controller:redirectToGithub", "columns": [{"field": "version", "headerName": "Version"}, {"field": "message", "headerName": "Message"}, {"field": "author", "headerName": "Author"}, {"field": "date", "headerName": "Date"}, {"field": "onMain", "headerName": "On Main", "cellVariant": {"variant": "status", "matchers": "@controller:getOnMainStatusColor"}}, {"field": "deployedToAppdev", "headerName": "Appdev", "cellVariant": {"variant": "status", "matchers": "@controller:getAppdevStatusColor"}}, {"field": "deployedToEverestRuns", "headerName": "ERE", "cellVariant": {"variant": "status", "matchers": "@controller:getEREStatusColor"}}, {"field": "deployedToSummit", "headerName": "Summit", "cellVariant": {"variant": "status", "matchers": "@controller:getSummitStatusColor"}}]}}]}, {"title": "AI Server", "components": [{"component": "Table", "customId": "aiCommitsTable", "section": {"editing": false, "grid": {"size": "12"}}, "props": {"data": "@binding:aiServerCommits", "variant": "light", "rowSelection": false, "onRowClicked": "@controller:redirectToGithub", "columns": [{"field": "version", "headerName": "Version"}, {"field": "message", "headerName": "Message"}, {"field": "author", "headerName": "Author"}, {"field": "date", "headerName": "Date"}, {"field": "deployedToAppdev", "headerName": "Appdev", "cellVariant": {"variant": "status", "matchers": "@controller:getAppdevStatusColor"}}, {"field": "deployedToEverestRuns", "headerName": "ERE", "cellVariant": {"variant": "status", "matchers": "@controller:getEREStatusColor"}}, {"field": "deployedToSummit", "headerName": "Summit", "cellVariant": {"variant": "status", "matchers": "@controller:getSummitStatusColor"}}]}}]}, {"title": "Python Server", "components": [{"component": "Table", "customId": "pythonServerCommitsTable", "section": {"editing": false, "grid": {"size": "12"}}, "props": {"data": "@binding:pythonServerCommits", "variant": "light", "rowSelection": false, "onRowClicked": "@controller:redirectToGithub", "columns": [{"field": "version", "headerName": "Version"}, {"field": "message", "headerName": "Message"}, {"field": "author", "headerName": "Author"}, {"field": "date", "headerName": "Date"}, {"field": "deployedToAppdev", "headerName": "Appdev", "cellVariant": {"variant": "status", "matchers": "@controller:getAppdevStatusColor"}}, {"field": "deployedToEverestRuns", "headerName": "ERE", "cellVariant": {"variant": "status", "matchers": "@controller:getEREStatusColor"}}, {"field": "deployedToSummit", "headerName": "Summit", "cellVariant": {"variant": "status", "matchers": "@controller:getSummitStatusColor"}}]}}]}, {"title": "Appserver", "components": [{"component": "Table", "customId": "appserverCommitsTable", "section": {"editing": false, "grid": {"size": "12"}}, "props": {"data": "@binding:appserverCommits", "variant": "light", "rowSelection": false, "onRowClicked": "@controller:redirectToGithub", "columns": [{"field": "version", "headerName": "Version"}, {"field": "message", "headerName": "Message"}, {"field": "author", "headerName": "Author"}, {"field": "date", "headerName": "Date"}, {"field": "onMain", "headerName": "On Main", "cellVariant": {"variant": "status", "matchers": "@controller:getOnMainStatusColor"}}, {"field": "deployedToAppdev", "headerName": "Appdev", "cellVariant": {"variant": "status", "matchers": "@controller:getAppdevStatusColor"}}, {"field": "deployedToEverestRuns", "headerName": "ERE", "cellVariant": {"variant": "status", "matchers": "@controller:getEREStatusColor"}}, {"field": "deployedToSummit", "headerName": "Summit", "cellVariant": {"variant": "status", "matchers": "@controller:getSummitStatusColor"}}]}}]}]}}}