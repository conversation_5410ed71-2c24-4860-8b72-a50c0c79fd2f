import type { ISession } from '@everestsystems/content-core';
import type {
  Commit,
  GitHubResponse,
} from '@pkg/everest.eve/actions/issueDashboard/util';
import {
  addIncrementingIds,
  deploymentStatus,
  getAllCommits,
} from '@pkg/everest.eve/actions/issueDashboard/util';
import { Eve } from '@pkg/everest.eve/types/Eve';

export default async function status(env: ISession): Promise<Commit[]> {
  const eveClient = await Eve.client(env);
  const [
    _appdevGitopsFileResponse,
    ereGitopsFileResponse,
    summitGitopsFileResponse,
  ] = await deploymentStatus(env);

  const owner = 'everestsystems';
  const repo = 'content-v2';
  const authors = new Set([
    'akhilnalliboina',
    'bhilprecht-everest',
    'b-hilprecht',
    'thenoireverest',
    'kodepapa',
    'bruno-carvalho-everest',
    'maximilian-jalea-everest',
    'jens-beyermann-everest',
    'Joilence',
    'kaiklede',
  ]);

  const deployedVersionsEre = extractDeployedPackageVersions(
    env,
    ereGitopsFileResponse
  );
  const deployedVersionsSummit = extractDeployedPackageVersions(
    env,
    summitGitopsFileResponse
  );

  const sortedCommits = await getAllCommits(env, owner, repo, 'main', 2000);

  const filteredCommits = sortedCommits.filter((commit: any) =>
    authors.has(commit.author?.login)
  );

  const commitDetails = await Promise.all(
    filteredCommits.map(async (commit: any) => {
      // Fetch manifests for all three packages
      const packageNames = ['everest.eve', 'everest.base.ai', 'everest.psa'];
      const manifestPromises = packageNames.map(async (packageName) => {
        const manifestUrl = `https://api.github.com/repos/${owner}/${repo}/contents/packages/${packageName}/manifest.json?ref=${commit.sha}`;
        try {
          const manifestResponse = (await eveClient.getGitHubAccess(
            manifestUrl
          )) as GitHubResponse;

          if (manifestResponse.status === 200) {
            const manifestContent = env.util.encoding
              .toBuffer(manifestResponse.data.content, 'base64')
              .toString('utf8');
            const manifest = JSON.parse(manifestContent);
            return { packageName, version: manifest.version };
          }
        } catch {
          // Package might not exist in this commit, continue
        }
        return { packageName, version: null };
      });

      const manifests = await Promise.all(manifestPromises);
      const packageVersions: Record<string, string | null> = {};
      for (const { packageName, version } of manifests) {
        packageVersions[packageName] = version;
      }

      const sandboxMergeRequestMatch = commit.commit.message.match(
        /SandboxMergeRequestId: \[(\d+)]\((.*?)\)/
      );
      const messageMatch = commit.commit.message.match(/SandboxName: (.*)/);

      // Use everest.eve version as the primary version for backward compatibility
      const primaryVersion = packageVersions['everest.eve'] || 'unknown';

      return {
        version: primaryVersion,
        message: messageMatch ? messageMatch[1] : commit.commit.message,
        url: sandboxMergeRequestMatch
          ? extractPathAndQuery(sandboxMergeRequestMatch[2])
          : commit.url,
        author: commit.author?.login || commit.commit.author.name,
        date: commit.commit.author.date,
        deployedToEverestRuns: isVersionDeployed(
          primaryVersion,
          deployedVersionsEre.eve
        ),
        deployedToSummit: isVersionDeployed(
          primaryVersion,
          deployedVersionsSummit.eve
        ),
        // by definition
        deployedToAppdev: true,
        // Package versions
        eveVersion: packageVersions['everest.eve'],
        baseAiVersion: packageVersions['everest.base.ai'],
        psaVersion: packageVersions['everest.psa'],
      };
    })
  );

  return addIncrementingIds(commitDetails);
}

// Helper function to compare versions
function isVersionDeployed(
  commitVersion: string,
  deployedVersion: string
): boolean {
  if (!commitVersion || !deployedVersion) {
    return false;
  }

  const [commitMajor, commitMinor, commitPatch] = commitVersion
    .split('.')
    .map(Number);
  const [deployedMajor, deployedMinor, deployedPatch] = deployedVersion
    .split('.')
    .map(Number);

  if (commitMajor > deployedMajor) {
    return false;
  }
  if (commitMajor < deployedMajor) {
    return true;
  }
  if (commitMinor > deployedMinor) {
    return false;
  }
  if (commitMinor < deployedMinor) {
    return true;
  }
  return commitPatch <= deployedPatch;
}

interface PackageVersions {
  eve: string;
  baseAi: string;
  psa: string;
}

function extractDeployedPackageVersions(
  env: ISession,
  gitopsFileResponse: GitHubResponse
): PackageVersions {
  const gitopsFileContent = env.util.encoding
    .toBuffer(gitopsFileResponse.data.content, 'base64')
    .toString('utf8');

  // Extract package versions for all three packages
  const eveVersionMatch = gitopsFileContent.match(
    /everest\.eve:(\d+\.\d+\.\d+)/
  );
  const baseAiVersionMatch = gitopsFileContent.match(
    /everest\.base\.ai:(\d+\.\d+\.\d+)/
  );
  const psaVersionMatch = gitopsFileContent.match(
    /everest\.psa:(\d+\.\d+\.\d+)/
  );

  return {
    eve: eveVersionMatch ? eveVersionMatch[1] : '',
    baseAi: baseAiVersionMatch ? baseAiVersionMatch[1] : '',
    psa: psaVersionMatch ? psaVersionMatch[1] : '',
  };
}

function extractPathAndQuery(url: string): string {
  const parsedUrl = url.replace(/^https?:\/\/[^/]+/, '');
  return parsedUrl.startsWith('/') ? parsedUrl : '/' + parsedUrl;
}
