import type { ISession } from '@everestsystems/content-core';
import { Eve } from '@pkg/everest.eve/types/Eve';

export interface Commit {
  version: string;
  message: string;
  author: string;
  date: string;
  url: string;
  onMain?: boolean;
  deployedToEverestRuns: boolean;
  deployedToSummit: boolean;
  deployedToAppdev: boolean;
  // Package versions
  eveVersion?: string;
  baseAiVersion?: string;
  psaVersion?: string;
}

export interface GitHubResponse {
  status: number;
  data: any;
}

export function sortCommitsByDate(commits: any[]): any[] {
  return commits.sort(
    (a, b) =>
      new Date(b.commit.author.date).getTime() -
      new Date(a.commit.author.date).getTime()
  );
}

export function addIncrementingIds(commits: Commit[]): Commit[] {
  return commits.map((commit, index) => ({
    id: index + 1,
    ...commit,
  }));
}

export async function deploymentStatus(env: ISession) {
  const eveClient = await Eve.client(env);
  const ereGitopsFileUrl =
    'https://api.github.com/repos/everestsystems/kubernetes-gitops/contents/components/everest-platform/customers/production/5a01987d-44cd-4978-a8aa-a8e4539a9493/tenant-helm-chart/tenant-helm-chart.yaml';

  const appdevGitopsFileUrl =
    'https://api.github.com/repos/everestsystems/kubernetes-gitops/contents/components/everest-platform/customers/internal/8b68e473-5988-4c03-ab6c-633198385d6a/tenant-helm-chart/tenant-helm-chart.yaml';

  const summitGitopsFileUrl =
    'https://api.github.com/repos/everestsystems/kubernetes-gitops/contents/components/everest-platform/customers/production/2ec05a12-a65d-499e-9813-90d5b9794fa6/tenant-helm-chart/tenant-helm-chart.yaml';

  const [
    appdevGitopsFileResponse,
    ereGitopsFileResponse,
    summitGitopsFileResponse,
  ] = (await Promise.all([
    eveClient.getGitHubAccess(appdevGitopsFileUrl),
    eveClient.getGitHubAccess(ereGitopsFileUrl),
    eveClient.getGitHubAccess(summitGitopsFileUrl),
  ])) as GitHubResponse[];

  return [
    appdevGitopsFileResponse,
    ereGitopsFileResponse,
    summitGitopsFileResponse,
  ];
}

export async function getAllCommits(
  env: ISession,
  owner: string,
  repo: string,
  branch: string,
  maxCommits: number
) {
  const eveClient = await Eve.client(env);
  let allCommits = [];
  let page = 1;
  const perPage = 100;

  while (allCommits.length < maxCommits) {
    const url = `https://api.github.com/repos/${owner}/${repo}/commits?sha=${branch}&per_page=${perPage}&page=${page}`;
    const response = (await eveClient.getGitHubAccess(url)) as GitHubResponse;

    if (response.status !== 200) {
      throw new Error(`Failed to fetch commits: ${response.status}`);
    }

    const commits = response.data;
    allCommits = allCommits.concat(commits);

    if (commits.length < perPage) {
      // We've reached the last page
      break;
    }

    page++;
  }

  return sortCommitsByDate(allCommits);
}

// Function to extract PR number from commit message
export function extractPRNumber(message: string): string | null {
  const match = message.match(/#(\d+)/);
  return match ? match[1] : null;
}
